#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
import sys
import time
import traceback
from typing import Dict, List, Tuple, Any, Optional, Union
import logging

# 导入统一配置
import config
from utils.logging_utils import get_logger

from RuleProcessor import RuleProcessor
from ScoreManager import ScoreManager
from TextProcessor import TextProcessor
from LLMInterface import LLMInterface

# 使用统一的日志配置
logger = get_logger(__name__)

class EssayGrader:
    """作文评分器：评估作文并生成评分结果"""
    
    def __init__(self,
                 title: str,
                 rules_file: str,
                 essay_file: str,
                 model_name: str = config.DEFAULT_MODEL,
                 api_key: str = None,
                 base_url: str = None,
                 verbose: bool = False,
                 debug_log_file: str = config.API_DEBUG_LOG):
        """
        初始化作文评分器
        
        Args:
            title (str): 题目
            rules_file (str): 规则文件路径
            essay_file (str): 作文文件路径
            model_name (str): 模型名称
            api_key (str): API密钥
            base_url (str): API基础URL
            verbose (bool): 是否启用详细输出
            debug_log_file (str): 调试日志文件路径
        """
        self.title = title
        self.rules_file = rules_file
        self.essay_file = essay_file
        self.verbose = verbose
        self.debug_log_file = debug_log_file
        
        # 读取文件内容
        self.rules_json = self._load_json_file(rules_file)
        self.essay_text = self._load_text_file(essay_file)
        
        # 初始化组件
        self.rule_processor = RuleProcessor(self.rules_json)
        self.text_processor = TextProcessor(self.essay_text)
        self.llm_interface = LLMInterface(model_name, api_key, base_url, debug_log_file)
        self.score_manager = ScoreManager()
        
        # 设置text_processor到score_manager
        self.score_manager.set_text_processor(self.text_processor)
        logger.info("已将text_processor设置到score_manager，启用智能匹配句子功能")
        
        # 设置text_processor到llm_interface，以便句子位置映射功能能够正常工作
        self.llm_interface.text_processor = self.text_processor
        logger.info("已将text_processor设置到llm_interface，启用句子位置映射功能")
    
    def _load_json_file(self, file_path: str) -> Dict:
        """
        加载JSON文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict: JSON数据
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载JSON文件 {file_path} 失败: {str(e)}")
            sys.exit(1)
    
    def _load_text_file(self, file_path: str) -> str:
        """
        加载文本文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 文本内容
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"加载文本文件 {file_path} 失败: {str(e)}")
            sys.exit(1)
    
    def _extract_rule_module(self, rule_path: str) -> str:
        """
        从规则路径中提取模块名
        
        Args:
            rule_path (str): 规则路径，如"首段-共5分.解释题干-共1分"、"第一段.解释题干"或"标题"
            
        Returns:
            str: 规则模块名，如"首段-共5分"、"第一段"或"标题"
        """
        # 处理可能包含"要求内容"的路径
        if ".要求内容." in rule_path:
            # 替换为普通点号，使其与普通路径处理一致
            rule_path = rule_path.replace(".要求内容.", ".")
        elif rule_path.endswith(".要求内容"):
            # 如果路径以"要求内容"结尾，直接移除
            rule_path = rule_path[:-5]
            
        parts = rule_path.split('.')
        if len(parts) > 1:
            return parts[0]
        return rule_path
    
    def _extract_rule_index(self, rule_path: str, module_name: str) -> int:
        """
        确定规则在模块中的索引
        
        Args:
            rule_path (str): 规则路径
            module_name (str): 模块名
            
        Returns:
            int: 规则索引（从1开始）
        """
        if not module_name:
            return 0
            
        # 获取该模块下的所有规则
        module_rules = [r for r in self.rule_processor.get_all_scorable_rules() 
                        if self._extract_rule_module(r["path"]) == module_name]
        
        # 按照原始顺序排序
        module_rules.sort(key=lambda x: x.get("order", 999999))
        
        # 找到当前规则的位置
        for i, rule in enumerate(module_rules, 1):
            if rule["path"] == rule_path:
                return i
                
        return 0
    
    def _check_essay_title(self):
        """
        检查作文是否有标题，并记录相关信息
        """
        if not self.essay_text:
            logger.warning("作文内容为空，无法检查标题")
            return
        
        # 不再重复处理文本，而是使用已经在TextProcessor中处理好的信息
        title = self.text_processor.title
        paragraphs = self.text_processor.paragraphs
        sentences_by_para = self.text_processor.sentences_by_para
        
        logger.info(f"作文第一行（标题）: '{title}'")
        
        # 如果有段落，记录第一段的摘要信息
        if paragraphs:
            first_para = paragraphs[0]
            para_preview = first_para[:50] + "..." if len(first_para) > 50 else first_para
            logger.info(f"作文第一段: '{para_preview}'")
            
            # 记录第一段的句子数量
            if 0 < len(sentences_by_para):
                first_para_sentences = sentences_by_para[0]
                logger.info(f"第一段包含 {len(first_para_sentences)} 个句子")
                
                # 记录前4个句子（如果存在）
                for i, sent in enumerate(first_para_sentences[:4]):
                    logger.info(f"第一段第{i+1}句: '{sent}'")
        
        # 将文章第一行设置为标题的上下文（保留这个操作以确保兼容性）
        self.text_processor.title = title
    
    def grade_essay(self) -> str:
        """
        评分作文
        
        Returns:
            str: 评分结果文本
        """
        # 检查作文标题
        self._check_essay_title()
        
        # 获取所有可评分的规则
        scorable_rules = self.rule_processor.get_all_scorable_rules()
        
        if self.verbose:
            logger.info(f"找到 {len(scorable_rules)} 条可评分规则")
        
        # 添加一个计数器跟踪通用评语出现次数
        generic_comment_counter = {"未提供需要评分的文本片段": 0}
        
        # 按模块分组规则
        rules_by_module = {}
        for rule_item in scorable_rules:
            module_name = self._extract_rule_module(rule_item["path"])
            if module_name not in rules_by_module:
                rules_by_module[module_name] = []
            rules_by_module[module_name].append(rule_item)
        
        # 添加日志，显示规则按模块分组情况
        logger.info(f"规则按模块分组情况:")
        for module_name, module_rules in rules_by_module.items():
            logger.info(f"模块 '{module_name}' 包含 {len(module_rules)} 条规则")
            for idx, rule in enumerate(module_rules, 1):
                logger.info(f"  - 规则 {idx}: 路径={rule['path']}, 范围={rule['rule'].get('范围', '全文')}")
        
        # 全局存储复合规则的评估结果，用于跨模块复用
        complex_rule_cache = {}
        
        # 对每个模块内的规则进行排序并评分
        for module_name, module_rules in rules_by_module.items():
            # 按照原始顺序排序，而不是按路径
            module_rules.sort(key=lambda x: x.get("order", 999999))
            
            # 设置当前规则模块
            self.text_processor.set_current_rule_module(module_name)
            
            # 存储规则依赖关系的映射，用于后续处理
            rule_dependencies = {}
            
            # 记录当前模块中规则的顺序
            logger.info(f"模块 '{module_name}' 中的规则顺序:")
            for idx, rule in enumerate(module_rules, 1):
                logger.info(f"  规则 {idx}: {rule['path']}")
            
            # 第一轮：对模块内的规则逐个评分，并找出匹配句子
            for rule_index, rule_item in enumerate(module_rules, 1):
                rule_path = rule_item["path"]
                rule_data = rule_item["rule"]
                
                # 如果规则中有"范围"字段，从作文中提取相应文本
                text_range = rule_data.get("范围", "全文")
                
                # 检查范围是否依赖于其他规则
                depends_on_rule = None
                is_complex_rule = False
                
                logger.info(f"处理规则 {rule_index}/{len(module_rules)} (路径: {rule_path}), 范围描述: '{text_range}'")
                
                # 检查是否是单一规则依赖
                rule_match = re.search(r'规则(\d+)的句子', text_range)
                if rule_match:
                    depends_on_rule = int(rule_match.group(1))
                    rule_dependencies[rule_index] = depends_on_rule
                    logger.info(f"规则 {rule_index} (路径: {rule_path}) 依赖于模块内的规则 {depends_on_rule}")
                    
                    # 获取依赖规则的匹配句子及位置信息
                    source_position = None
                    source_text = None
                    
                    # 检查是否有被依赖规则的匹配句子
                    if module_name in self.text_processor.rule_matching_sentences and depends_on_rule in self.text_processor.rule_matching_sentences[module_name]:
                        source_text = self.text_processor.rule_matching_sentences[module_name][depends_on_rule]
                        logger.info(f"获取到依赖规则 {depends_on_rule} 的匹配句子: {source_text[:50]}...")
                        
                        # 对于依赖规则的范围，直接使用被依赖规则的匹配句子作为评估文本
                        if re.match(r'^规则\d+的句子$', text_range):
                            logger.info(f"范围为 '{text_range}', 直接使用依赖规则的匹配句子")
                            text_to_evaluate = source_text
                            # 预先设置匹配句子变量
                            matching_sentence = source_text
                    
                    # 获取位置信息（如果存在）
                    if hasattr(self.text_processor, 'rule_matching_sentence_positions') and \
                       module_name in self.text_processor.rule_matching_sentence_positions and \
                       depends_on_rule in self.text_processor.rule_matching_sentence_positions[module_name]:
                        source_position = self.text_processor.rule_matching_sentence_positions[module_name][depends_on_rule]
                        logger.info(f"获取到依赖规则 {depends_on_rule} 的句子位置: {source_position}")
                
                # 检查是否是复合规则依赖（如"规则2或4或6的句子之后"）
                complex_rule_match = re.search(r'规则([\d或]+)的句子之后', text_range)
                if complex_rule_match:
                    is_complex_rule = True
                    rule_nums_str = complex_rule_match.group(1)
                    logger.info(f"规则 {rule_index} (路径: {rule_path}) 是复合依赖规则: 依赖于规则 {rule_nums_str}")
                    
                    # 尝试从全局缓存中获取这个复合规则的评估结果
                    cache_key = f"复合规则:{rule_nums_str}"
                    if cache_key in complex_rule_cache:
                        cached_result = complex_rule_cache[cache_key]
                        logger.info(f"使用缓存的复合规则评估结果: {cache_key}")
                        
                        # 复用缓存的评估结果
                        self.score_manager.add_score(
                            rule_path,
                            cached_result["text"],
                            cached_result["score"],
                            cached_result["comment"],
                            cached_result.get("thought_process"),
                            text_range  # 添加范围描述
                        )
                        continue
                    
                    # 在当前模块内检查是否已经评估过类似的规则
                    already_processed = False
                    for prev_idx, prev_rule in enumerate(module_rules[:rule_index-1], 1):
                        prev_range = prev_rule.get("rule", {}).get("范围", "")
                        if prev_range == text_range:
                            logger.info(f"跳过重复的复合规则评估: 规则 {rule_index} 与规则 {prev_idx} 有相同的范围描述: {text_range}")
                            already_processed = True
                            
                            # 复用之前在该模块中的评分结果
                            for score_item in self.score_manager.scores:
                                if score_item["rule_path"] == prev_rule["path"]:
                                    self.score_manager.add_score(
                                        rule_path,
                                        score_item["text"],
                                        score_item["score"],
                                        score_item["comment"],
                                        score_item.get("thought_process"),
                                        text_range  # 添加范围描述
                                    )
                                    logger.info(f"复用规则 {prev_idx} 的评分结果: {score_item['score']}分 - {score_item['comment']}")
                                    break
                            break
                    
                    if already_processed:
                        continue
                
                # 使用大模型提取符合范围描述的文本
                # 对于复杂的范围描述（包含规则引用或特定句子位置描述），使用大模型提取
                text_to_evaluate = ""
                # 修改复杂范围的判断逻辑，排除简单的"第X句"格式
                # 简单的"第X句"应该使用get_text_by_range处理
                is_complex_range = re.search(r'规则|句子之后|尾句|末句|最后一句|第.*段.*句|第.*句.*段|之后', text_range) is not None
                
                # 进一步排除纯粹的"第X句"格式（无论是中文数字还是阿拉伯数字）
                if re.match(r'^第[一二三四五六七八九十\d]+句$', text_range):
                    is_complex_range = False
                
                if is_complex_range:
                    logger.info(f"使用大模型提取复杂范围描述的文本: '{text_range}'")
                    text_to_evaluate = self.text_processor.llm_extract_text_by_range(
                        text_range, 
                        self.llm_interface,
                        rule_index
                    )
                else:
                    # 对于简单范围描述，使用原始方法提取
                    logger.info(f"使用原有方法提取简单范围描述的文本: '{text_range}'")
                    text_to_evaluate = self.text_processor.get_text_by_range(text_range)
                
                if not text_to_evaluate:
                    logger.warning(f"无法为规则 {rule_path} 提取有效文本（范围：{text_range}）")
                    logger.error(f"⚠️ 发现空文本片段! 模块: {module_name}, 规则: {rule_index}, 路径: {rule_path}, 范围描述: '{text_range}'")
                    text_to_evaluate = "未找到内容"  # 或者直接用空字符串
                    comment = "未提供需要评分的文本片段，无法判断是否满足评分标准，因此得0分。"
                    generic_comment_counter["未提供需要评分的文本片段"] += 1
                    logger.error(f"⚠️ 添加通用评语: '{comment}' (第{generic_comment_counter['未提供需要评分的文本片段']}次)")
                
                if self.verbose:
                    logger.info(f"评分规则：{rule_path}")
                    logger.info(f"评分范围：{text_range}")
                    logger.info(f"待评分文本：{text_to_evaluate[:50]}...")
                
                # 调用大模型进行评分
                # 将规则路径添加到规则数据中
                rule_data_with_path = rule_data.copy()
                rule_data_with_path["path"] = rule_path

                try:
                    eval_result = self.llm_interface.evaluate_text(
                        self.title, rule_data_with_path, text_to_evaluate, rule_index=rule_index
                    )

                    # 如果是复合规则，将结果存入全局缓存
                    if is_complex_rule and complex_rule_match:
                        rule_nums_str = complex_rule_match.group(1)
                        cache_key = f"复合规则:{rule_nums_str}"
                        complex_rule_cache[cache_key] = {
                            "text": text_to_evaluate,
                            "score": eval_result["score"],
                            "comment": eval_result["comment"],
                            "thought_process": eval_result.get("thought_process")
                        }
                        logger.info(f"缓存复合规则评估结果: {cache_key}")
                except Exception as e:
                    # 处理连续解析错误的异常
                    error_msg = str(e)
                    logger.error(f"评估规则 {rule_path} 时发生错误: {error_msg}")
                    print(f"\n[严重错误] 评估规则时发生错误: {error_msg}", flush=True)
                    
                    # 创建一个包含错误信息的评估结果
                    eval_result = {
                        "score": 0,
                        "comment": f"评分失败: {error_msg}",
                        "matching_sentence": "",
                        "error": True
                    }
                    
                    # 添加该评分结果到评分管理器
                    self.score_manager.add_score(
                        rule_path, 
                        text_to_evaluate, 
                        eval_result["score"], 
                        eval_result["comment"],
                        None,  # 没有思考过程
                        text_range  # 添加范围描述
                    )
                    
                    # 继续处理下一个规则
                    continue
                
                # 根据评分结果判断是否找到匹配句子
                matching_sentence = None
                sentence_position = None

                # 检查是否是文本提取任务
                if eval_result.get("is_text_extraction", False):
                    # 对于文本提取，不需要匹配句子，直接跳过
                    pass
                # 常规评分任务处理
                else:
                    # 从评分结果中获取匹配句子
                    matching_sentence = eval_result.get("matching_sentence", "")
                    
                    # 获取位置信息
                    sentence_position_desc = eval_result.get("sentence_position", "")
                    
                    # 检查规则范围是否是首段最后一句话或第N段最后一句话
                    is_last_sentence_rule = False
                    if "范围" in rule_data:
                        range_desc = rule_data.get("范围", "")
                        last_sentence_match = re.search(r'(首段|第(\d+)段)最后一句话', range_desc)
                        if last_sentence_match:
                            is_last_sentence_rule = True
                            # 确定段落索引
                            if last_sentence_match.group(1) == "首段":
                                para_index = 0  # 首段就是第一段(索引0)
                            else:
                                para_index = int(last_sentence_match.group(2)) - 1  # 转为0-based索引
                                
                            # 获取该段落的最后一句的索引
                            para_sentences = self.text_processor.sentences_by_para[para_index] if para_index < len(self.text_processor.sentences_by_para) else []
                            if para_sentences:
                                last_sent_index = len(para_sentences) - 1
                                sentence_position = (para_index, last_sent_index)
                                logger.info(f"检测到'最后一句话'规则范围: 段落{para_index+1}的第{last_sent_index+1}句(最后一句)")
                    
                    # 如果不是最后一句话规则，则使用常规方式解析位置信息
                    if not is_last_sentence_rule and sentence_position_desc:
                        # 记录API返回的位置信息
                        logger.info(f"API返回的句子位置: {sentence_position_desc}")
                        
                        # 使用text_processor解析位置描述
                        if hasattr(self.text_processor, 'parse_sentence_position_description'):
                            # 创建模块到段落索引的映射
                            module_to_para_index = {
                                "标题": -1,  # 特殊标记，表示是标题
                                "首段": 0,    # 正文的第一段
                                "第一段": 0,   # 正文的第一段
                                "第二段": 1,
                                "第三段": 2,
                                "第四段": 3,
                                "第五段": 4,
                                "第六段": 5,
                                "第七段": 6,
                                "第八段": 7,
                                "第九段": 8,
                                "尾段": len(self.text_processor.paragraphs) - 1 if self.text_processor.paragraphs else 0,
                                "最后一段": len(self.text_processor.paragraphs) - 1 if self.text_processor.paragraphs else 0
                            }
                            
                            # 获取当前处理的段落索引，用于相对位置描述
                            current_para_index = module_to_para_index.get(module_name, 0)
                            
                            try:
                                # 解析位置描述为具体的段落和句子索引
                                sentence_position = self.text_processor.parse_sentence_position_description(
                                    sentence_position_desc, current_para_index)
                                para_idx, sent_idx = sentence_position
                                logger.info(f"解析的句子位置: 第{para_idx+1}段第{sent_idx+1}句")
                            except Exception as e:
                                logger.error(f"解析句子位置时出错: {str(e)}")
                
                # 记录该规则评估的文本和匹配句子
                self.text_processor.set_rule_context(
                    module_name, rule_index, text_to_evaluate, matching_sentence
                )
                
                # 如果有匹配的句子和有效的位置信息，单独存储句子位置
                if matching_sentence:
                    # 检查是否依赖于其他规则
                    depends_on_rule = None
                    is_pure_dependency = False  # 是否是纯依赖（如"规则X的句子"）
                    
                    if "范围" in rule_data:
                        range_desc = rule_data.get("范围", "")
                        # 匹配"规则X的句子"模式（纯依赖）
                        pure_rule_match = re.search(r'^规则(\d+)的句子$', range_desc)
                        if pure_rule_match:
                            depends_on_rule = int(pure_rule_match.group(1))
                            is_pure_dependency = True
                            logger.info(f"检测到纯依赖规则: 规则{rule_index} 纯依赖于规则{depends_on_rule}")
                        else:
                            # 匹配复杂依赖模式（如"规则X的句子之后..."）
                            complex_rule_match = re.search(r'规则(\d+)的句子', range_desc)
                            if complex_rule_match:
                                depends_on_rule = int(complex_rule_match.group(1))
                                is_pure_dependency = False
                                logger.info(f"检测到复杂依赖规则: 规则{rule_index} 复杂依赖于规则{depends_on_rule}, 范围: {range_desc}")
                    
                    # 对于纯依赖规则，继承被依赖规则的位置
                    if is_pure_dependency and depends_on_rule and hasattr(self.text_processor, 'rule_matching_sentence_positions') and \
                       module_name in self.text_processor.rule_matching_sentence_positions and \
                       depends_on_rule in self.text_processor.rule_matching_sentence_positions[module_name]:
                        # 继承源规则的位置信息
                        source_position = self.text_processor.rule_matching_sentence_positions[module_name][depends_on_rule]
                        self.text_processor.store_matching_sentence(
                            module_name, rule_index, matching_sentence, source_position
                        )
                        logger.info(f"纯依赖规则 {rule_index} 继承规则 {depends_on_rule} 的句子位置: {source_position}")
                    # 对于复杂依赖或有API位置信息的情况，优先使用API返回的位置信息
                    elif sentence_position:
                        # 使用LLM返回的位置信息
                        self.text_processor.store_matching_sentence(
                            module_name, rule_index, matching_sentence, sentence_position
                        )
                        logger.info(f"使用API返回的位置信息存储规则 {rule_index} 的匹配句子: {sentence_position}")
                    else:
                        # 仅存储句子内容，没有位置信息
                        self.text_processor.store_matching_sentence(
                            module_name, rule_index, matching_sentence
                        )
                        logger.info(f"成功存储规则 {rule_index} 的匹配句子（无位置信息）")
                
                # 检查评语是否包含通用评语关键词
                if "未提供需要评分的文本片段" in eval_result["comment"]:
                    generic_comment_counter["未提供需要评分的文本片段"] += 1
                    logger.error(f"⚠️ 添加通用评语: '{eval_result['comment']}' (第{generic_comment_counter['未提供需要评分的文本片段']}次)")
                
                # 添加评分结果
                self.score_manager.add_score(
                    rule_path, 
                    text_to_evaluate, 
                    eval_result["score"], 
                    eval_result["comment"],
                    eval_result.get("thought_process"),
                    text_range  # 添加范围描述
                )
                
                if self.verbose:
                    logger.info(f"评分结果：{eval_result['score']}分 - {eval_result['comment']}")
                    if matching_sentence:  # 添加空值检查
                        logger.info(f"匹配句子：{matching_sentence}")
                    else:
                        logger.info("没有匹配句子")
        
        # 在格式化前记录统计信息
        logger.info(f"格式化前评分结果总数: {len(self.score_manager.scores)}")
        logger.info(f"通用评语'未提供需要评分的文本片段'出现次数: {generic_comment_counter['未提供需要评分的文本片段']}")
        
        # 格式化评分后的文本
        scored_text = self.score_manager.format_scored_text(self.essay_text)
        
        # 如果启用了详细输出，打印评分摘要
        if self.verbose:
            logger.info("\n" + self.score_manager.get_score_summary())
        
        return scored_text
    
    def save_result(self, output_file: str):
        """
        保存评分结果到文件
        
        Args:
            output_file (str): 输出文件路径
        """
        # 评分
        try:
            scored_text = self.grade_essay()
            
            # 检查是否有评分出错的情况
            error_scores = [score for score in self.score_manager.scores if score.get("comment", "").startswith("评分失败:")]
            if error_scores:
                logger.warning(f"有 {len(error_scores)} 条规则评分出错")
                for err_score in error_scores:
                    logger.warning(f"规则 {err_score['rule_path']} 评分失败: {err_score['comment']}")
                
                # 如果错误数量超过总规则数量的一半，给出警告
                if len(error_scores) > len(self.score_manager.scores) / 2:
                    logger.error(f"超过一半的规则评分失败，结果可能不可靠！")
                    print(f"\n[严重警告] 超过一半的规则评分失败，结果可能不可靠！", flush=True)
            
            # 保存结果
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(scored_text)
            logger.info(f"评分结果已保存到 {output_file}")
            
            # 即使有错误也继续执行，但会记录警告
            if error_scores:
                print(f"\n[警告] 评分完成，但有 {len(error_scores)} 条规则评分出错。详情请查看日志。", flush=True)
                return
                
        except Exception as e:
            logger.error(f"保存评分结果时出错: {str(e)}")
            # 记录详细的错误堆栈
            logger.error(traceback.format_exc())
            print(f"\n[错误] 评分过程中发生严重错误: {str(e)}", flush=True)
            
            try:
                # 尝试保存已经评分的部分
                if hasattr(self, 'score_manager') and self.score_manager.scores:
                    partial_scored_text = self.score_manager.format_scored_text(self.essay_text)
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(partial_scored_text)
                    logger.info(f"部分评分结果已保存到 {output_file}")
                    print(f"部分评分结果已保存到 {output_file}", flush=True)
            except Exception as save_err:
                logger.error(f"保存部分评分结果时出错: {str(save_err)}")
                print(f"\n[错误] 无法保存部分评分结果: {str(save_err)}", flush=True)
