[build-system]
requires = ["setuptools>=42.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "essay-grader"
version = "0.1.0"
description = "智能作文评分系统"
requires-python = ">=3.8"
license = {text = "MIT"}

[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'

[tool.ruff]
target-version = "py38"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "N",  # pep8-naming
    "UP", # pyupgrade
    "C",  # flake8-comprehensions
    "B",  # flake8-bugbear
    "A",  # flake8-builtins
    "T20", # flake8-print
    "Q",  # flake8-quotes
    "PT", # flake8-pytest-style
    "RUF", # Ruff-specific rules
]
ignore = [
    "E501",  # line too long, handled by black
    "PT004", # fixtures without return values are ignored
]

[tool.ruff.isort]
known-first-party = ["utils", "tests"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
pythonpath = ["."]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "openai.*",
    "httpx.*",
    "colorama.*",
    "nltk.*",
    "dotenv.*"
]
ignore_missing_imports = true