#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from pathlib import Path
from typing import Dict, List, Optional

try:
    from dotenv import load_dotenv  # type: ignore
except ImportError:
    # 如果没有 python-dotenv，定义一个空的替代函数
    def load_dotenv(dotenv_path=None, override=False):
        pass

# 项目根目录
ROOT_DIR = Path(__file__).parent

# 加载环境变量 - 仅在此处加载一次，其他模块从这里导入值
env_path = os.path.join(ROOT_DIR, '.env')
load_dotenv(dotenv_path=env_path, override=True)

# ==================== API 配置 ====================
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
DEFAULT_MODEL = os.getenv("MODEL_NAME", "gemini-2.0-flash-exp")

# API调用配置
API_CALL_INTERVAL = int(os.getenv("INTERVAL", "0"))
API_RETRY_TIMES = int(os.getenv("API_RETRY_TIMES", "3"))
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "30"))
API_WAIT_TIME_SINGLE_KEY = int(os.getenv("API_WAIT_TIME_SINGLE_KEY", "10"))
API_WAIT_TIME_MULTI_KEY = int(os.getenv("API_WAIT_TIME_MULTI_KEY", "1"))
MAX_PARSE_ERROR_RETRIES = int(os.getenv("MAX_PARSE_ERROR_RETRIES", "5"))

# ==================== 日志配置 ====================
LOG_FILE = os.path.join(ROOT_DIR, "app.log")
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
API_DEBUG_LOG = os.path.join(ROOT_DIR, "api_debug.log")

# ==================== 文件路径配置 ====================
DEFAULT_OUTPUT_FILE = "批改结果.txt"
LOGS_DIR = os.path.join(ROOT_DIR, "logs")

# 确保日志目录存在
os.makedirs(LOGS_DIR, exist_ok=True)

def parse_api_keys(api_key_str: str) -> List[str]:
    """
    解析逗号分隔的多个API密钥
    
    Args:
        api_key_str (str): 逗号分隔的API密钥字符串
        
    Returns:
        List[str]: API密钥列表
    """
    # 分割并去除空格
    keys = [key.strip() for key in api_key_str.split(',')]
    # 过滤掉空字符串
    return [key for key in keys if key]

# 从环境变量解析API密钥
API_KEYS = parse_api_keys(OPENAI_API_KEY) if OPENAI_API_KEY else []

# ==================== 文本处理配置 ====================
# 中文数字映射
CHINESE_TO_ARABIC = {
    "一": 1, "二": 2, "三": 3, "四": 4, "五": 5,
    "六": 6, "七": 7, "八": 8, "九": 9, "十": 10
}

# 段落映射配置
def get_default_paragraph_map(paragraphs_count: int) -> Dict[str, int]:
    """
    根据段落数量生成默认段落映射

    Args:
        paragraphs_count (int): 段落数量

    Returns:
        Dict[str, int]: 段落名称到索引的映射
    """
    return {
        "首段": 0,
        "第一段": 0,
        "第二段": 1 if paragraphs_count > 1 else 0,
        "第三段": 2 if paragraphs_count > 2 else 0,
        "第四段": 3 if paragraphs_count > 3 else 0,
        "第五段": 4 if paragraphs_count > 4 else 0,
        "第六段": 5 if paragraphs_count > 5 else 0,
        "第七段": 6 if paragraphs_count > 6 else 0,
        "第八段": 7 if paragraphs_count > 7 else 0,
        "第九段": 8 if paragraphs_count > 8 else 0,
        "尾段": max(0, paragraphs_count - 1),
        "最后一段": max(0, paragraphs_count - 1),
    }

# ==================== 初始化函数 ====================
def initialize_logging():
    """
    初始化日志配置

    这个函数应该在应用启动时调用一次，避免在每个模块中重复配置日志
    """
    import logging

    # 避免重复配置
    if logging.getLogger().handlers:
        return

    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format=LOG_FORMAT,
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8', mode='w'),
            logging.StreamHandler()
        ]
    )