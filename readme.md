# 智能作文评分系统

该项目是一个基于LLM的智能作文评分系统，能够根据预定义的规则自动分析和评分作文。

## 主要改进

根据代码分析，我们对系统进行了以下改进：

1. **模块化重构**：将硬编码常量和逻辑分离到专门的模块中，提高代码可维护性和可读性。
2. **范围解析器**：使用结构化语法树处理范围描述，替代原有的大量if-else条件判断。
3. **异常处理**：引入自定义异常类型，提供更明确的错误消息和上下文。
4. **日志系统**：改进日志记录，支持按日期滚动和更一致的彩色输出。
5. **API管理**：增强API密钥管理和速率限制功能，提高稳定性。
6. **测试覆盖**：添加全面的单元测试，确保核心功能的可靠性。
7. **代码质量工具**：集成Ruff和Black进行代码风格检查和格式化。
8. **CI/CD**：设置GitHub Actions自动运行测试和代码质量检查。

## 项目结构

```
.
├── app.py                 # 主应用程序，负责解析命令行参数并调用EssayGrader
├── EssayGrader.py         # 作文评分器核心类，整合各个模块
├── LLMInterface.py        # 大语言模型接口，处理API调用和密钥管理
├── ScoreManager.py        # 评分管理器，负责记录和格式化评分结果
├── TextProcessor.py       # 文本处理器，负责文本分割和范围提取
├── RuleProcessor.py       # 规则处理器，负责解析和处理JSON格式的批改规则
├── llm_core.py            # (可能包含与LLM相关的核心逻辑或工具函数)
├── llm_config.py          # (可能包含LLM相关的配置信息)
├── trans.py               # Markdown规则转JSON的辅助工具
├── requirements.txt       # 项目依赖
├── pyproject.toml         # 项目配置 (PEP 518)
├── .env                   # 环境变量配置文件 (示例)
├── .github/               # GitHub Actions CI/CD 配置
│   └── workflows/
│       └── python-tests.yml # Python 测试工作流
├── utils/                 # (如果仍有通用工具函数，否则可移除或整合)
└── tests/                 # 测试目录
    ├── __init__.py
    ├── test_llm_interface.py # (示例测试文件，根据实际情况调整)
    ├── test_rule_processor.py
    ├── test_text_processor.py
    └── ...                  # 其他测试文件
```

## 安装和使用

1. 克隆项目并安装依赖：

```bash
git clone <repository-url>
cd essay-grader
pip install -r requirements.txt
```

2. 设置API密钥环境变量：

```bash
# Windows
set OPENAI_API_KEY=your_api_key

# Linux/Mac
export OPENAI_API_KEY=your_api_key
```

3. 运行程序：

```bash
python app.py --rules_file rules.json --essay_file essay.txt --output_file result.txt
```

## 测试

运行单元测试：

```bash
pytest
```

代码质量检查：

```bash
ruff check .
black --check .
```

## 后续工作

1. 进一步增强测试覆盖率，特别是针对复杂的评分规则、边缘情况和错误处理逻辑。
2. 优化和扩展对多种语言文本及国际化场景的支持。
3. 持续完善项目文档，包括更详细的API参考、模块说明和用户指南。
4. 探索更先进的文本分析技术和LLM应用，以提高评分的准确性和智能化程度。
5. 根据用户反馈和实际应用场景，持续迭代和优化系统功能。

## 项目简介

本项目是一个智能批改系统，专为公务员考试申论部分的作文评分而设计。系统能够根据预定义的批改规则，使用大语言模型（LLM）对考生作文进行自动评分，实现公正、高效的作文批改。

`app.py` 是系统的主要执行文件，它通过以下步骤工作：
1. 读取题目、批改规则和学员作文
2. 解析批改规则，将规则扁平化为易于处理的格式
3. 分析学员作文，将其分割为段落和句子
4. 按规则模块依次评分，对每条规则调用大模型API进行评估
5. 汇总分数，生成最终评分结果

## 如何运行

### 基本用法
```bash
python app.py --rules 批改规则.json --essay 学员作文.txt
```

### 完整命令行参数
```bash
python app.py --rules 批改规则.json --essay 学员作文.txt [--title 题目.txt] [--output 批改结果.txt] [--model MODEL_NAME] [--api-key API_KEY] [--base-url API_BASE_URL] [--verbose] [--interactive] [--debug-log api_debug.log]
```

### 命令行参数说明
- `--rules`, `-r`: 指定批改规则JSON文件路径（必需）
- `--essay`, `-e`: 指定学员作文文件路径（必需）
- `--title`, `-t`: 指定题目文件路径（可选，如不提供则从作文文件第一行获取）
- `--output`, `-o`: 指定输出结果文件路径（默认为"批改结果.txt"）
- `--model`, `-m`: 指定使用的大模型（默认为环境变量中的MODEL_NAME）
- `--api-key`, `-k`: 指定API密钥（如不指定则从环境变量读取，支持多个API密钥用逗号分隔）
- `--base-url`, `-b`: 指定API基础URL（如不指定则从环境变量读取）
- `--verbose`, `-v`: 启用详细输出模式，显示更多日志信息
- `--interactive`, `-i`: 启用交互模式，允许用户在评分过程中进行交互
- `--debug-log`, `-d`: 指定API调试日志文件路径（默认为"api_debug.log"），记录规则处理的提示词和响应

### 运行示例

1. 基本用法：
```bash
python app.py --rules 批改规则.json --essay 学员作文.txt
```

2. 指定模型和输出文件：
```bash
python app.py --rules 批改规则.json --essay 学员作文.txt --model gemini-2.0-flash-exp --output 结果.txt
```

3. 使用多个API密钥：
```bash
python app.py --rules 批改规则.json --essay 学员作文.txt --api-key "key1,key2,key3"
```

4. 交互模式：
```bash
python app.py --rules 批改规则.json --essay 学员作文.txt --interactive
```

## 依赖项

运行此脚本需要以下Python库：
- openai>=0.27.0：与OpenAI API通信
- nltk>=3.7：用于自然语言处理，如句子分割
- colorama>=0.4.6：用于终端彩色输出
- python-dotenv>=1.0.0：用于加载环境变量
- requests>=2.28.0：用于HTTP请求
- httpx>=0.24.1：用于异步HTTP请求
- pytest>=7.4.0：用于运行单元测试
- pytest-mock>=3.11.1：pytest的mocking插件
- ruff>=0.1.0：用于代码风格检查和自动修复
- black>=24.1.0：用于代码格式化

可以使用以下命令安装所有依赖：
```bash
pip install -r requirements.txt
```

## 配置

系统使用`.env`文件进行配置。复制 `.env.example` 文件为 `.env` 并填入实际配置值。

### API配置
- `OPENAI_API_KEY`：OpenAI API密钥或其他兼容API密钥（支持多个密钥，用逗号分隔）
- `OPENAI_BASE_URL`：API基础URL，默认为"https://api.openai.com/v1"
- `MODEL_NAME`：默认使用的大模型名称，如"gemini-2.0-flash-exp"

### API调用配置
- `INTERVAL`：API调用间隔（秒），用于限制请求频率
- `API_RETRY_TIMES`：API重试次数，默认为3
- `API_TIMEOUT`：API超时时间（秒），默认为30
- `API_WAIT_TIME_SINGLE_KEY`：单个API密钥失败后的等待时间（秒），默认为10
- `API_WAIT_TIME_MULTI_KEY`：多个API密钥轮换时的等待时间（秒），默认为1
- `MAX_PARSE_ERROR_RETRIES`：最大解析错误重试次数，默认为5

### 日志配置
- `LOG_LEVEL`：日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL），默认为INFO

### 配置示例
```bash
# 复制配置文件模板
cp .env.example .env

# 编辑配置文件
# 填入您的API密钥和其他配置
```

您也可以使用命令行参数覆盖`.env`文件中的配置。

## 系统设计

### 架构概述

系统由以下几个核心组件构成：

1. **TextProcessor**：文本处理器，负责段落分割、句子切分和文本片段提取
2. **RuleProcessor**：规则处理器，负责解析和处理JSON格式的批改规则
3. **LLMInterface**：大模型接口，负责与大语言模型API通信
4. **ScoreManager**：评分管理器，负责管理和格式化评分结果
5. **EssayGrader**：作文评分器，整合以上组件完成作文评分

### 处理流程

1. 读取题目、批改规则和学员作文
2. 解析批改规则，将规则扁平化为易于处理的格式
3. 分析学员作文，将其分割为段落和句子
4. 按规则模块依次评分，对每条规则：
   - 确定评分范围（如"全文"、"首段"、"第二段第一句"等）
   - 提取对应文本片段
   - 调用大模型API进行评分
   - 将评分结果记录并插入原文
5. 汇总分数，生成最终评分结果

## 原始资料与输出内容

### 原始资料
1. 题目
2. 批改规则（JSON格式）
3. 学员作文

### 输出内容
按格式要求输出学员得分，在原文中插入评分注释。

#### 示例输出

```
在法治政府建设的各个环节中，行政执法往往受到很高的关注。"力"、"理"、"利"在行政执法工作中是重要的组成部分。"力"是指行政执法工作中的公信力，"理"是指在热法过程中要讲清事理、法理、情理，"利"是指在执法过程中要维护人民群众的利益。【1分 出现题干中的中心句子或词语并加以解释 力 利 理完整出现】【1分 出现提醒：解释不完整】【1分 抄写题干中的句子或词语，不要完整抄完再解释。应该抄一部分加以解释，再抄另外部分再解释。】"力"和"理"为执法工作带来许多好处，不断维护利益，促进法治政府建设，因此，在行政执法工作中，"力"与"理"带来了"利"。【1分 明确亮出中心论点 属于表达3 赋1分】
```

## 主要组件详解

### 1. TextProcessor（文本处理器）

**功能**：
- 将作文分割为段落和句子
- 提取特定文本片段（如标题、首段、第二段等）
- 支持复杂的文本范围描述解析
- 维护段落和句子的索引映射
- 处理规则评分的上下文信息

**关键方法**：
- `_split_paragraphs`：文本分段
- `_split_sentences`：段落分句
- `get_text_by_range`：根据范围描述提取文本
- `llm_extract_text_by_range`：使用大模型提取复杂范围文本
- `store_matching_sentence`：存储规则匹配的句子

### 2. RuleProcessor（规则处理器）

**功能**：
- 解析JSON格式的批改规则
- 将嵌套规则扁平化为列表
- 识别可评分规则
- 解析规则分数

**关键方法**：
- `_flatten_rules`：将嵌套规则扁平化
- `get_all_scorable_rules`：获取所有可评分规则
- `get_rules_for_section`：获取特定段落的规则
- `parse_score_from_rule`：从规则中解析分数

### 3. LLMInterface（大模型接口）

**功能**：
- 管理与大语言模型API的通信
- 提供API密钥轮询和负载均衡
- 构建提示词并解析响应
- 错误处理和重试机制

**关键方法**：
- `evaluate_text`：评估文本是否符合规则
- `_call_openai`：调用OpenAI API
- `_parse_response`：解析API响应
- `rotate_to_next_api_key`：轮换到下一个API密钥
- `_build_prompt`：构建提示词

### 4. ScoreManager（评分管理器）

**功能**：
- 记录和管理评分结果
- 在原文中插入评分注释
- 计算总分和生成评分汇总

**关键方法**：
- `add_score`：添加评分结果
- `format_scored_text`：格式化评分文本
- `get_score_summary`：获取评分汇总

### 5. EssayGrader（作文评分器）

**功能**：
- 整合其他组件完成作文评分
- 加载和处理文件
- 管理评分流程

**关键方法**：
- `grade_essay`：评分作文
- `save_result`：保存评分结果
- `_load_json_file`：加载JSON文件
- `_load_text_file`：加载文本文件

## 系统特性

1. **智能文本分析**
   - 支持中文和英文文本分析
   - 灵活的段落和句子识别
   - 复杂的文本范围描述解析

2. **高级规则处理**
   - 支持多层嵌套的规则结构
   - 规则依赖关系处理
   - 复合规则支持

3. **大模型API管理**
   - 多API密钥支持和负载均衡
   - 自动重试和错误处理
   - 详细的调试日志

4. **高度可配置**
   - 支持不同的大模型
   - 可配置的API参数
   - 灵活的批改规则

## 辅助工具

### Markdown规则转JSON转换器 (trans.py)

**功能**：将具有特定格式的Markdown文件转换为结构化的JSON格式

**支持的解析模式**：
- `standard`：标准处理
- `structured`：结构化处理（默认）
- `simple`：简单处理

**使用方法**：
```
python trans.py --input 批改规则.md --output 批改规则.json
```

## 特殊处理与优化

1. **中文数字处理**：支持中文数字到阿拉伯数字的转换
2. **API密钥轮询**：在多个API密钥之间轮换，提高稳定性
3. **复杂范围解析**：支持"规则x的句子之后"等复杂范围描述
4. **句子位置描述**：智能解析和生成句子位置描述
5. **缓存机制**：对复杂规则评估结果进行缓存，提高效率