#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

# 导入统一配置
import config
from utils.logging_utils import get_logger

# 使用统一的日志配置
logger = get_logger(__name__)

class ScoreManager:
    """分数管理器：用于管理和格式化评分结果"""
    
    def __init__(self):
        """初始化分数管理器"""
        self.scores = []
        self.total_score = 0
        self.scored_text = ""
        self.text_processor = None  # 将在EssayGrader中设置
    
    def set_text_processor(self, text_processor):
        """设置文本处理器"""
        self.text_processor = text_processor
        
    def add_score(self, rule_path: str, text_segment: str, score: int, comment: str, thought_process=None, range_desc=None):
        """
        添加评分结果
        
        Args:
            rule_path (str): 规则路径
            text_segment (str): 文本片段
            score (int): 分数
            comment (str): 评语
            thought_process (str, optional): 大模型的思考过程
            range_desc (str, optional): 规则的范围描述，如"全段"、"第一句"等
        """
        self.scores.append({
            "rule_path": rule_path,
            "text": text_segment,
            "score": score,
            "comment": comment,
            "thought_process": thought_process,
            "range": range_desc  # 新增：存储范围描述
        })
        self.total_score += score
    
    def format_scored_text(self, original_text: str) -> str:
        """
        格式化评分后的文本
        
        Args:
            original_text (str): 原始文本
            
        Returns:
            str: 格式化后的文本，包含评分注解
        """
        # 确保text_processor已设置
        if not self.text_processor:
            logger.warning("文本处理器未设置，无法使用智能匹配句子功能")
            
        # 创建一个文本段落到评分注解的映射
        segment_annotations = {}
        # 用于跟踪已添加的注解，避免重复
        annotation_tracker = set()
        # 用于跟踪每种评语，无论应用于哪个文本段落
        comment_tracker = {}
        # 用于跟踪通用评语
        generic_comment_tracker = set()
        # 用于跟踪每个模块的规则序号
        module_rule_counters = {}
        
        logger.info(f"格式化前评分结果总数: {len(self.scores)}")
        
        # 记录每种评语的数量，用于调试
        comment_count = {}
        
        # 将文本分为段落，用于后续定位
        all_paragraphs = [p for p in original_text.split('\n') if p.strip()]
        
        # 提取标题和正文段落
        title = all_paragraphs[0] if all_paragraphs else ""
        paragraphs = all_paragraphs[1:] if len(all_paragraphs) > 1 else []  # 排除标题后的段落
        
        # 计算通用评语数量，用于调试
        generic_comment_count = sum(1 for score_item in self.scores if "未提供需要评分的文本片段" in score_item["comment"])
        logger.info(f"通用评语'未提供需要评分的文本片段'出现次数: {generic_comment_count}")
        
        logger.info(f"开始格式化评分结果，总共 {len(self.scores)} 条评分...")
        
        # 分割每个段落的句子，提取出第一句和第二句等句子
        paragraph_sentences = []
        
        for p in paragraphs:
            sentences = re.split(r'([。！？\.][\""\']?)', p)
            
            # 将句子和标点组合起来
            combined_sentences = []
            i = 0
            while i < len(sentences) - 1:
                if i + 1 < len(sentences):
                    combined_sentences.append(sentences[i] + sentences[i+1])
                    i += 2
                else:
                    if sentences[i].strip():  # 修改：检查是否为空
                        combined_sentences.append(sentences[i])
                    i += 1
            
            # 修改：处理最后一个部分
            if i < len(sentences) and sentences[i].strip():
                combined_sentences.append(sentences[i])
            
            # 修改：过滤空句子并去除首尾空白
            combined_sentences = [s.strip() for s in combined_sentences if s.strip()]
            
            paragraph_sentences.append(combined_sentences)
        
        logger.info(f"提取到标题: '{title}'")
        logger.info(f"提取到 {len(paragraphs)} 个正文段落")
        
        # 创建段落模块映射，确保规则注解添加到正确的段落
        # 重要：这里的索引是针对正文段落的索引，不包含标题
        module_to_paragraph = {
            "标题": -1,  # 特殊标记，表示是标题
            "首段": 0,    # 正文的第一段
            "第一段": 0,   # 正文的第一段
            "第二段": 1 if len(paragraphs) > 1 else 0,
            "第三段": 2 if len(paragraphs) > 2 else 0,
            "第四段": 3 if len(paragraphs) > 3 else 0,
            "第五段": 4 if len(paragraphs) > 4 else 0,
            "第六段": 5 if len(paragraphs) > 5 else 0,
            "第七段": 6 if len(paragraphs) > 6 else 0,
            "第八段": 7 if len(paragraphs) > 7 else 0,
            "第九段": 8 if len(paragraphs) > 8 else 0,
            "尾段": len(paragraphs) - 1 if len(paragraphs) > 0 else 0,
            "最后一段": len(paragraphs) - 1 if len(paragraphs) > 0 else 0
        }
        
        # 记录标题的注解和每个段落的注解（直接根据段落分配注解，而不是根据文本匹配）
        title_annotations = []
        paragraph_annotations = [[] for _ in paragraphs]
        # 记录每个段落每个句子的注解 - 使用字典结构支持任意第n句
        paragraph_sentence_annotations = [{} for _ in paragraphs]
        
        # 对每个评分结果进行处理
        
        for score_item in self.scores:
            # 初始化句子索引变量，确保每个评分项都有独立的初始值
            sentence_index = -1
            
            rule_path = score_item["rule_path"]
            text_segment = score_item["text"]
            score = score_item["score"]
            comment = score_item["comment"]
            # 添加范围描述检查
            rule_range = score_item.get("range", "")
            
            # 提取规则名称和模块名称
            parts = rule_path.split('.')
            
            # 提取规则名称 - 通常是path中最后一部分
            rule_name = parts[-1] if len(parts) > 0 else rule_path
            
            # 提取模块名称 - 通常是path中第一部分
            module_name = parts[0] if len(parts) > 0 else "未知模块"
            
            # 确定该规则应适用于哪个段落
            target_paragraph_index = module_to_paragraph.get(module_name, -2)
            
            # 为每个模块维护独立的规则计数器
            if module_name not in module_rule_counters:
                module_rule_counters[module_name] = 1
            else:
                module_rule_counters[module_name] += 1
            
            # 获取当前模块的规则序号
            rule_index = module_rule_counters[module_name]
            
            # 统计评语出现次数
            if comment not in comment_count:
                comment_count[comment] = 0
            comment_count[comment] += 1
            
            # 创建评分注解，格式为【模块名规则X：规则名：分数 评语】
            score_annotation = f"【{module_name}规则{rule_index}：{rule_name}：{score}分 {comment}】"
            
            text_preview = text_segment[:30] + "..." if text_segment and len(text_segment) > 30 else text_segment or "空文本"
            logger.info(f"处理第 {rule_index}/{len(self.scores)} 条评分 - 规则: {module_name}.{rule_name}, 文本: {text_preview}, 得分: {score}, 评语: {comment}")
            
            # 检查是否是通用评语
            is_generic_comment = any(keyword in comment for keyword in [
                "未提供需要评分的文本片段",
                "无法判断是否满足评分标准",
                "未提及",
                "未体现",
                "不符合",
                "不得分"
            ])
            
            # 如果是通用评语，只保留一次
            if is_generic_comment:
                if comment in generic_comment_tracker:
                    logger.warning(f"跳过重复的通用评语 (第{comment_count[comment]}次): {comment[:50]}...")
                    continue
                generic_comment_tracker.add(comment)
                logger.info(f"添加通用评语 (首次): {comment[:50]}...")
            
            # 特殊情况处理：对于特定规则或错误消息，只保留一次
            is_special_case = False
            
            # 检查是否是"规则2或4或6的句子之后"相关评语
            if "在规则2、4、6之后" in comment or "未找到能体现" in comment:
                is_special_case = True
                if comment in comment_tracker:
                    logger.info(f"跳过重复的特殊评语 (第{comment_count[comment]}次): {comment[:30]}...")
                    continue
                comment_tracker[comment] = True
                logger.info(f"添加特殊评语 (首次): {comment[:30]}...")
            
            # 检查是否是API失败消息
            if "无法连接到API服务" in comment or "评分失败" in comment:
                is_special_case = True
                if comment in comment_tracker:
                    logger.info(f"跳过重复的API失败消息 (第{comment_count[comment]}次): {comment[:30]}...")
                    continue
                comment_tracker[comment] = True
                logger.info(f"添加API失败消息 (首次): {comment[:30]}...")
            
            # 对于非特殊情况，使用模块+规则索引+评语组合防止重复
            if not is_special_case and not is_generic_comment:
                annotation_key = f"{module_name}-{rule_index}::{score_annotation}"
                if annotation_key in annotation_tracker:
                    logger.info(f"跳过重复的评分注解 (第{comment_count[comment]}次): {score_annotation[:30]}...")
                    continue
                annotation_tracker.add(annotation_key)
                logger.info(f"添加常规评语: {score_annotation[:30]}...")
            
            # 如果是标题的评语，添加到标题注解列表
            if target_paragraph_index == -1:  # 标题
                title_annotations.append(score_annotation)
                logger.info(f"将评语 {score_annotation[:30]}... 添加到标题")
            # 如果可以确定目标段落，则检查是否需要特殊处理特定句子
            elif 0 <= target_paragraph_index < len(paragraph_annotations):
                # 优先使用从LLM获取的句子位置信息
                if self.text_processor and hasattr(self.text_processor, 'rule_matching_sentence_positions') and \
                   module_name in self.text_processor.rule_matching_sentence_positions and \
                   rule_index in self.text_processor.rule_matching_sentence_positions[module_name]:
                    # 使用LLM返回的位置信息
                    para_idx, sent_idx = self.text_processor.rule_matching_sentence_positions[module_name][rule_index]
                    # 只有当目标段落匹配时才使用这个信息
                    if para_idx == target_paragraph_index:
                        sentence_index = sent_idx
                        logger.info(f"使用从LLM获取的句子位置信息: 段落{para_idx+1}的第{sent_idx+1}句")
                
                # 如果没有找到LLM返回的位置信息，再尝试其他方法解析
                if sentence_index == -1:
                    # 检查规则范围是否为"首段最后一句话"或"第N段最后一句话"
                    last_sentence_match = re.search(r'(首段|第(\d+)段)最后一句话', rule_range)
                    if last_sentence_match:
                        # 确定段落索引
                        if last_sentence_match.group(1) == "首段":
                            para_idx = 0  # 首段就是第一段(索引0)
                        else:
                            para_idx = int(last_sentence_match.group(2)) - 1  # 转为0-based索引
                            
                        # 只有当目标段落匹配时才使用这个信息
                        if para_idx == target_paragraph_index and para_idx < len(paragraph_sentences):
                            # 获取该段落的最后一句的索引
                            if paragraph_sentences[para_idx]:
                                last_sent_idx = len(paragraph_sentences[para_idx]) - 1
                                sentence_index = last_sent_idx
                                logger.info(f"检测到'最后一句话'规则范围: 段落{para_idx+1}的第{last_sent_idx+1}句(最后一句)")
                
                    # 如果不是最后一句话规则，再尝试处理依赖于其他规则的情况
                    if sentence_index == -1:
                        # 首先处理依赖于其他规则的情况（如"规则X的句子"）
                        rule_dependency_match = re.search(r'规则(\d+)的句子', rule_range)
                        if rule_dependency_match:
                            depends_on_rule = int(rule_dependency_match.group(1))
                            logger.info(f"检测到规则依赖: {rule_path} 依赖于规则 {depends_on_rule}")
                            
                            # 查找被依赖规则的匹配句子在段落中的位置
                            if self.text_processor and hasattr(self.text_processor, 'rule_matching_sentences') and \
                               module_name in self.text_processor.rule_matching_sentences and \
                               depends_on_rule in self.text_processor.rule_matching_sentences[module_name]:
                                
                                dependent_sentence = self.text_processor.rule_matching_sentences[module_name][depends_on_rule]
                                logger.info(f"找到依赖规则 {depends_on_rule} 的匹配句子: {dependent_sentence[:30]}...")
                                
                                # 在段落的句子列表中查找该句子的位置
                                if target_paragraph_index >= 0 and target_paragraph_index < len(paragraph_sentences):
                                    sentences = paragraph_sentences[target_paragraph_index]
                                    for i, sent in enumerate(sentences):
                                        clean_sent = sent.strip() if sent else ""
                                        clean_dependent = dependent_sentence.strip() if dependent_sentence else ""
                                        
                                        if clean_sent == clean_dependent or clean_dependent in clean_sent:
                                            sentence_index = i
                                            logger.info(f"找到依赖规则的匹配句子在段落中的位置: 第 {i+1} 句")
                                            break
                
                # 解析"第n句"格式（如果没有处理依赖规则匹配）
                if sentence_index == -1 and rule_range.startswith("第") and rule_range.endswith("句"):
                    sentence_number_str = rule_range[1:-1]  # 去掉"第"和"句"
                    # 处理中文数字转换
                    chinese_digits = {"一": 0, "二": 1, "三": 2, "四": 3, "五": 4,
                                      "六": 5, "七": 6, "八": 7, "九": 8, "十": 9}
                    if sentence_number_str in chinese_digits:
                        sentence_index = chinese_digits[sentence_number_str]  # 转为0-based索引
                    else:
                        # 尝试转换为数字
                        try:
                            sentence_index = int(sentence_number_str) - 1  # 转为0-based索引
                        except ValueError:
                            # 无法解析为数字，保持默认值
                            pass
                
                # 对所有规则都使用TextProcessor寻找匹配的句子
                if sentence_index == -1 and self.text_processor and target_paragraph_index < len(paragraphs):
                    # 获取目标段落文本
                    paragraph_text = paragraphs[target_paragraph_index]
                    
                    # 使用文本处理器查找匹配的句子
                    if text_segment:
                        # 设置当前处理的规则模块
                        self.text_processor.set_current_rule_module(module_name)
                        
                        # 查找匹配该规则的句子
                        matching_sentence = self.text_processor.find_matching_sentence_for_rule(
                            module_name, rule_index, paragraph_text
                        )
                        
                        if matching_sentence:
                            # 在段落中查找匹配句子的索引
                            all_sentences = paragraph_sentences[target_paragraph_index]
                            found_matching_index = -1
                            
                            # 使用更精确的匹配方式
                            for i, sent in enumerate(all_sentences):
                                # 清理两个字符串以便更精确地比较
                                clean_sent = sent.strip() if sent else ""
                                clean_matching = matching_sentence.strip() if matching_sentence else ""
                                
                                # 首先尝试精确匹配
                                if clean_sent == clean_matching:
                                    found_matching_index = i
                                    logger.info(f"在段落中找到精确匹配的句子: {i}, 句子: {sent[:30]}...")
                                    break
                                    
                                # # 如果精确匹配失败，尝试包含匹配
                                # elif clean_matching and clean_matching in clean_sent:
                                #     found_matching_index = i
                                #     logger.info(f"在段落中找到包含匹配的句子: {i}, 句子: {sent[:30]}...")
                                #     break
                                    
                                # # 还可以尝试更宽松的匹配方式 - 核心词匹配
                                # # 对于包含"力"、"理"、"利"的句子的特殊处理
                                # elif ("力" in clean_sent and "理" in clean_sent and "利" in clean_sent and
                                #       "力" in clean_matching and "理" in clean_matching and "利" in clean_matching):
                                #     found_matching_index = i
                                #     logger.info(f"在段落中找到包含关键词'力'、'理'、'利'的句子: {i}, 句子: {sent[:30]}...")
                                #     break
                            
                            if found_matching_index >= 0:
                                sentence_index = found_matching_index
                                logger.info(f"为规则 '{rule_name}' 设置匹配句子索引: {found_matching_index}")
                            else:
                                # 如果找不到匹配的句子，记录日志并尝试直接在段落中搜索匹配句子
                                logger.warning(f"未能在段落中找到匹配句子，尝试直接搜索: {matching_sentence[:50]}...")
                                
                                # 尝试在原始段落文本中直接定位
                                try:
                                    start_pos = paragraph_text.find(matching_sentence)
                                    if start_pos >= 0:
                                        # 计算这个位置之前有多少个句子结束符
                                        before_text = paragraph_text[:start_pos]
                                        sentence_count = sum(1 for match in re.finditer(r'[。！？\.][\""\']?', before_text))
                                        found_matching_index = sentence_count
                                        logger.info(f"通过文本位置找到匹配句子的索引: {found_matching_index}")
                                        sentence_index = found_matching_index
                                    else:
                                        # 还是找不到，默认使用最后一句
                                        all_sentences = paragraph_sentences[target_paragraph_index]
                                        last_sentence_index = max(0, len(all_sentences) - 1)
                                        logger.warning(f"直接搜索也未找到匹配句子，默认使用最后一句")
                                        sentence_index = last_sentence_index
                                except Exception as e:
                                    logger.error(f"搜索匹配句子时发生错误: {str(e)}")
                                    # 出错时默认使用最后一句
                                    all_sentences = paragraph_sentences[target_paragraph_index]
                                    last_sentence_index = max(0, len(all_sentences) - 1)
                                    sentence_index = last_sentence_index
                        else:
                            # 如果没找到匹配的句子，使用最后一句
                            all_sentences = paragraph_sentences[target_paragraph_index]
                            last_sentence_index = max(0, len(all_sentences) - 1)
                            logger.info(f"未找到匹配规则 '{rule_name}' 的句子，默认使用最后一句")
                            sentence_index = last_sentence_index
                
                # 根据句子索引添加评语到适当位置
                if sentence_index >= 0:
                    # 检查规则范围是否为"全段"
                    is_full_paragraph_rule = score_item.get("range", "") == "全段"
                    
                    if is_full_paragraph_rule:
                        # 对于"全段"范围的规则，直接添加到段落级评语
                        paragraph_annotations[target_paragraph_index].append(score_annotation)
                        logger.info(f"'全段'范围规则: 将评语 {score_annotation[:30]}... 附加到段落 {target_paragraph_index+1} 末尾")
                    else:
                        # 其他规则按原逻辑添加到特定句子
                        # 添加到特定句子的评语列表
                        if sentence_index not in paragraph_sentence_annotations[target_paragraph_index]:
                            paragraph_sentence_annotations[target_paragraph_index][sentence_index] = []
                        paragraph_sentence_annotations[target_paragraph_index][sentence_index].append(score_annotation)
                        
                        # 记录日志
                        sentence_number = sentence_index + 1  # 转为1-based编号用于显示
                        logger.info(f"将评语 {score_annotation[:30]}... 添加到正文段落 {target_paragraph_index+1} 的第{sentence_number}句")
                else:
                    # 添加到段落级评语
                    paragraph_annotations[target_paragraph_index].append(score_annotation)
                    logger.info(f"将评语 {score_annotation[:30]}... 添加到正文段落 {target_paragraph_index+1}")
            else:
                # 如果无法确定目标段落，则尝试通过文本匹配定位
                if text_segment:
                    # 将评分注解添加到映射中
                    if text_segment in segment_annotations:
                        segment_annotations[text_segment].append(score_annotation)
                        logger.info(f"向已有文本片段添加评语: {text_segment[:30]}... -> {score_annotation[:30]}...")
                    else:
                        segment_annotations[text_segment] = [score_annotation]
                        logger.info(f"为新文本片段创建评语: {text_segment[:30]}... -> {score_annotation[:30]}...")
        
        # 记录追踪统计
        logger.info(f"通用评语去重结果: {len(generic_comment_tracker)} 个保留, 剩余被跳过")
        for comment in generic_comment_tracker:
            logger.info(f"保留的通用评语: {comment[:50]}...")
        
        logger.info(f"特殊评语去重结果: {len(comment_tracker)} 个保留")
        for comment in comment_tracker:
            logger.info(f"保留的特殊评语: {comment[:50]}...")
        
        logger.info(f"评语出现次数统计:")
        for comment, count in comment_count.items():
            if count > 1:
                logger.warning(f"评语出现 {count} 次: {comment[:50]}...")
        
        # 首先处理标题并应用评分注解
        result_parts = []
        if title:
            # 添加标题和它的注解
            result_parts.append(title)
            if title_annotations:
                annotation_text = "".join(title_annotations)
                result_parts[-1] += annotation_text
                logger.info(f"为标题添加了 {len(title_annotations)} 个评语")
        
        # 然后按段落应用评分注解，处理任意句子的评语
        for idx, paragraph in enumerate(paragraphs):
            current_paragraph = paragraph
            paragraph_result = "\n"
            
            # 检查是否有句子评语
            has_any_sentence_annotation = idx < len(paragraph_sentence_annotations) and paragraph_sentence_annotations[idx]
            
            if has_any_sentence_annotation:
                # 获取当前段落的句子列表
                sentences = paragraph_sentences[idx]
                current_pos = 0
                
                # 按序号从小到大处理每个有评语的句子
                for sentence_idx in sorted(paragraph_sentence_annotations[idx].keys()):
                    if sentence_idx < 0 or sentence_idx >= len(sentences):
                        continue  # 跳过无效的句子索引
                    
                    sentence = sentences[sentence_idx]
                    if not sentence:
                        continue  # 跳过空句子
                    
                    # 查找当前句子在段落中的位置
                    sentence_start = current_paragraph.find(sentence, current_pos)
                    if sentence_start < 0:
                        continue  # 找不到句子，跳过
                    
                    # 添加句子前的文本
                    if sentence_start > current_pos:
                        paragraph_result += current_paragraph[current_pos:sentence_start]
                    
                    # 添加句子及其评语
                    paragraph_result += sentence + "".join(paragraph_sentence_annotations[idx][sentence_idx])
                    
                    # 更新当前位置
                    current_pos = sentence_start + len(sentence)
                
                # 添加剩余的未处理文本
                if current_pos < len(current_paragraph):
                    paragraph_result += current_paragraph[current_pos:]
            else:
                # 没有句子级评语，整段处理
                paragraph_result += current_paragraph
            
            # 添加段落级评语（如果有）
            if paragraph_annotations[idx]:
                paragraph_result += "".join(paragraph_annotations[idx])
            
            result_parts.append(paragraph_result)
            
            # 记录日志
            if has_any_sentence_annotation:
                for sentence_idx, annotations in paragraph_sentence_annotations[idx].items():
                    sentence_number = sentence_idx + 1  # 转为1-based编号用于显示
                    logger.info(f"为正文段落 {idx+1} 的第{sentence_number}句添加了 {len(annotations)} 个评语")
            if paragraph_annotations[idx]:
                logger.info(f"为正文段落 {idx+1} 添加了 {len(paragraph_annotations[idx])} 个段落级评语")
        
        # 将所有部分连接起来
        scored_text = "".join(result_parts)
        
        # 然后处理无法确定段落的评分注解（基于文本匹配）
        annotation_applied = 0
        
        for segment, annotations in segment_annotations.items():
            if segment in scored_text:
                # 将所有注解连接在一起
                all_annotations = "".join(annotations)
                scored_text = scored_text.replace(segment, f"{segment}{all_annotations}")
                annotation_applied += len(annotations)
                logger.info(f"通过文本匹配应用 {len(annotations)} 个评语到文本片段: {segment[:30]}...")
            else:
                if segment:  # 只对非空文本片段记录警告
                    logger.warning(f"文本片段未在原文中找到，无法应用评语: {segment[:30]}...")
        
        # 计算应用的总评语数
        total_annotations = (annotation_applied + 
                           sum(len(anns) for anns in paragraph_annotations) + 
                           len(title_annotations) + 
                           sum(len(annotations) for para_annotations in paragraph_sentence_annotations 
                               for annotations in para_annotations.values()))
        
        logger.info(f"最终应用了 {total_annotations} 个评语到原文中")
        
        # 添加总分统计
        scored_text += f"\n\n总分：{self.total_score}分"
        
        return scored_text
    
    def get_score_summary(self) -> str:
        """
        获取评分摘要
        
        Returns:
            str: 评分摘要
        """
        summary = "评分摘要：\n"
        for idx, score_item in enumerate(self.scores, 1):
            rule_name = score_item["rule_path"].split(".")[-1]
            score = score_item["score"]
            comment = score_item["comment"]
            summary += f"{idx}. {rule_name}：{score}分 - {comment}\n"
        
        summary += f"\n总分：{self.total_score}分"
        return summary
