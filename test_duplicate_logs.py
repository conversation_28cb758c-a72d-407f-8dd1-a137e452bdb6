#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""测试日志重复输出问题"""

import config
from utils.logging_utils import get_logger
from LLMInterface import LLMInterface

# 初始化日志配置
config.initialize_logging()

# 获取不同的日志记录器
app_logger = get_logger("app")
llm_logger = get_logger("LLMInterface")
essay_logger = get_logger("EssayGrader")

print("=== 测试日志输出（应该每条只出现一次）===")
app_logger.info("这是 app 的日志消息")
llm_logger.info("这是 LLMInterface 的日志消息")
essay_logger.info("这是 EssayGrader 的日志消息")

# 测试同一个记录器多次输出
print("\n=== 测试同一记录器多次输出 ===")
llm_logger.info("第一条 LLMInterface 消息")
llm_logger.info("第二条 LLMInterface 消息")

print("\n=== 测试完成 ===")
