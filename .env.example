# 作文评分系统配置文件示例
# 复制此文件为 .env 并填入实际配置值

# ==================== API 配置 ====================
# OpenAI API 密钥（必填）
# 支持多个密钥，用逗号分隔，系统会自动轮换使用
OPENAI_API_KEY=your_api_key_here

# API 基础 URL（可选）
# 默认为 OpenAI 官方 API，也可以使用兼容的第三方服务
OPENAI_BASE_URL=https://api.openai.com/v1

# 默认模型名称（可选）
# 推荐使用 gemini-2.0-flash-exp 或 gpt-4
MODEL_NAME=gemini-2.0-flash-exp

# ==================== API 调用配置 ====================
# API 调用间隔时间（秒）
INTERVAL=0

# API 重试次数
API_RETRY_TIMES=3

# API 超时时间（秒）
API_TIMEOUT=30

# 单个 API 密钥失败后的等待时间（秒）
API_WAIT_TIME_SINGLE_KEY=10

# 多个 API 密钥轮换时的等待时间（秒）
API_WAIT_TIME_MULTI_KEY=1

# 最大解析错误重试次数
MAX_PARSE_ERROR_RETRIES=5

# ==================== 日志配置 ====================
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
