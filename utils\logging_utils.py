#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import sys
from typing import Optional
from pathlib import Path

import config

def setup_logger(name: Optional[str] = None, 
                 log_file: Optional[str] = None,
                 log_level: Optional[str] = None,
                 log_format: Optional[str] = None) -> logging.Logger:
    """
    设置并配置日志记录器
    
    Args:
        name (Optional[str]): 日志记录器名称，默认为根记录器
        log_file (Optional[str]): 日志文件路径，默认使用config.LOG_FILE
        log_level (Optional[str]): 日志级别，默认使用config.LOG_LEVEL
        log_format (Optional[str]): 日志格式，默认使用config.LOG_FORMAT
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 从配置或参数中获取值
    log_file = log_file or config.LOG_FILE
    log_level_str = log_level or config.LOG_LEVEL
    log_format = log_format or config.LOG_FORMAT
    
    # 将字符串日志级别转换为整数常量
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    log_level_int = level_map.get(log_level_str.upper(), logging.INFO)
    
    # 获取或创建指定名称的日志记录器
    logger = logging.getLogger(name)
    
    # 如果日志记录器已经被配置过，则直接返回
    if logger.handlers:
        return logger
    
    # 设置日志级别
    logger.setLevel(log_level_int)
    
    # 创建文件处理器
    try:
        file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='w')
        file_handler.setFormatter(logging.Formatter(log_format))
        logger.addHandler(file_handler)
    except (IOError, PermissionError) as e:
        print(f"警告: 无法创建日志文件 '{log_file}': {e}", file=sys.stderr)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(console_handler)
    
    return logger

def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取已配置的日志记录器

    Args:
        name (Optional[str]): 日志记录器名称

    Returns:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)

    # 检查根日志记录器是否已配置
    root_logger = logging.getLogger()
    if root_logger.handlers:
        # 根日志记录器已配置，直接返回子日志记录器
        return logger

    # 如果根日志记录器未配置，则配置当前记录器
    if not logger.handlers:
        return setup_logger(name)

    return logger

# 为API调试创建专门的日志记录器
def get_api_debug_logger() -> logging.Logger:
    """
    获取API调试专用的日志记录器
    
    Returns:
        logging.Logger: API调试日志记录器
    """
    logger = logging.getLogger("api_debug")
    
    # 如果记录器已配置，则直接返回
    if logger.handlers:
        return logger
    
    # 设置日志级别为DEBUG
    logger.setLevel(logging.DEBUG)
    
    # 创建文件处理器
    try:
        file_handler = logging.FileHandler(
            config.API_DEBUG_LOG, 
            encoding='utf-8', 
            mode='w'
        )
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(message)s')
        )
        logger.addHandler(file_handler)
    except (IOError, PermissionError) as e:
        print(f"警告: 无法创建API调试日志文件 '{config.API_DEBUG_LOG}': {e}", file=sys.stderr)
    
    return logger 