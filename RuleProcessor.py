#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, List, Tuple, Any, Optional, Union
import re

# 导入统一配置
import config
from utils.logging_utils import get_logger

# 使用统一的日志配置
logger = get_logger(__name__)

class RuleProcessor:
    """规则处理器：用于解析和处理批改规则"""
    
    def __init__(self, rules_json: Dict):
        """
        初始化规则处理器
        
        Args:
            rules_json (Dict): 批改规则JSON数据
        """
        self.rules = rules_json
        self.flattened_rules = self._flatten_rules(rules_json)
        
        # 添加日志，显示扁平化后的所有规则
        logger.info(f"扁平化后的规则总数: {len(self.flattened_rules)}")
        for idx, rule in enumerate(self.flattened_rules, 1):
            path = rule.get("path", "无路径")
            if "rule" in rule and isinstance(rule["rule"], dict):
                rule_content = rule["rule"]
                range_desc = rule_content.get("范围", "全文")
                logger.info(f"规则 {idx}: 路径={path}, 有规则内容, 范围={range_desc}")
            else:
                logger.info(f"规则 {idx}: 路径={path}, 无规则内容")
    
    def _flatten_rules(self, rules: Dict, parent_key="", result=None, order_counter=None) -> List[Dict]:
        """
        将嵌套的规则扁平化为列表
        
        Args:
            rules (Dict): 嵌套的规则字典
            parent_key (str): 父级键名
            result (List): 结果列表
            order_counter (Dict): 顺序计数器
            
        Returns:
            List[Dict]: 扁平化的规则列表
        """
        if result is None:
            result = []
        
        if order_counter is None:
            order_counter = {"count": 0}
        
        # 忽略作文顶层键
        if parent_key == "作文":
            for key, value in rules.items():
                self._flatten_rules(value, key, result, order_counter)
            return result
        
        # 特殊处理标题节点和其他有分数的主要段落节点
        if "分额" in rules and parent_key in ["2.标题", "3.第一段", "4.第二段"]:
            order_counter["count"] += 1
            # 复制规则，避免修改原始数据
            rule_copy = rules.copy()
            
            # 特殊处理要求内容子节点
            if "要求内容" in rule_copy and isinstance(rule_copy["要求内容"], dict):
                # 将要求内容子节点的属性提取到规则的顶层
                for k, v in rule_copy["要求内容"].items():
                    rule_copy[k] = v
                # 删除要求内容子节点，防止重复处理
                del rule_copy["要求内容"]
            
            # 添加段落规则节点
            rule_info = {
                "path": parent_key,
                "rule": rule_copy,
                "order": order_counter["count"]
            }
            result.append(rule_info)
            logger.info(f"添加主要段落节点: {parent_key}, 分额: {rule_copy.get('分额')}")
        
        # 处理各种规则情况
        for key, value in rules.items():
            full_key = f"{parent_key}.{key}" if parent_key else key
            
            # 跳过规则的内部属性（如"规则"、"数据"、"要求"等）
            if key in ["规则", "数据", "要求", "范围", "其他", "分额"]:
                continue
                
            # 跳过已经在主要段落处理中考虑的要求内容
            if key == "要求内容" and parent_key in ["2.标题", "3.第一段", "4.第二段"]:
                continue
            
            # 如果是字符串值，且不是规则属性，添加为规则
            if isinstance(value, str):
                order_counter["count"] += 1
                result.append({
                    "path": full_key,
                    "value": value,
                    "order": order_counter["count"]  # 添加原始顺序
                })
            # 如果是字典类型，检查是否为评分规则
            elif isinstance(value, dict):
                # 判断是否为具体评分规则节点
                # 只要包含"规则"字段或包含"要求"和"数据"字段，就视为规则节点
                is_rule_node = "规则" in value or ("要求" in value and "数据" in value)
                
                # 如果是规则节点，添加到结果列表
                if is_rule_node:
                    order_counter["count"] += 1
                    rule_info = {
                        "path": full_key,
                        "rule": value,
                        "order": order_counter["count"]  # 添加原始顺序
                    }
                    result.append(rule_info)
                else:
                    # 不是叶子规则节点，需要递归处理
                    # 跟踪段落节点的分数信息，但不添加到结果列表中
                    if "分额" in value and not is_rule_node and key not in ["要求内容"]:
                        # 仅记录顺序计数，不添加到结果列表
                        order_counter["count"] += 1
                        logger.debug(f"发现段落节点: {full_key}，包含分额: {value['分额']}，不作为评分规则添加")
                    
                    # 无论如何都递归处理子节点
                    self._flatten_rules(value, full_key, result, order_counter)
        
        return result
    
    def get_rules_for_section(self, section_name: str) -> List[Dict]:
        """
        获取特定段落的规则
        
        Args:
            section_name (str): 段落名称，如"首段"、"第二段"等
            
        Returns:
            List[Dict]: 规则列表
        """
        return [rule for rule in self.flattened_rules 
                if section_name in rule["path"] or 
                (section_name + ".要求内容") in rule["path"]]
    
    def get_all_scorable_rules(self) -> List[Dict]:
        """
        获取所有可评分的规则
        
        Returns:
            List[Dict]: 规则列表
        """
        scorable_rules = []
        
        for rule_index, rule in enumerate(self.flattened_rules, 1):
            logger.info(f"检查规则 {rule_index}: {rule['path']}")
            
            if "rule" in rule and isinstance(rule["rule"], dict):
                rule_dict = rule["rule"]
                logger.info(f"  规则字典内容: {rule_dict}")
                
                # 判断条件1：直接包含"规则"字段，或者同时包含"分额"和"要求"字段
                condition1 = "规则" in rule_dict or ("分额" in rule_dict and "要求" in rule_dict)
                # 判断条件2：包含"分额"和"要求内容"子节点，且"要求内容"中包含"规则"字段
                condition2 = False
                if "分额" in rule_dict and "要求内容" in rule_dict and isinstance(rule_dict["要求内容"], dict):
                    logger.info(f"  检查要求内容: {rule_dict['要求内容']}")
                    if "③规则" in rule_dict["要求内容"] or "规则" in rule_dict["要求内容"]:
                        condition2 = True
                # 判断条件3：是主要段落节点（直接包含分额）
                condition3 = "分额" in rule_dict and rule["path"] in ["2.标题", "3.第一段", "4.第二段"]
                
                if rule["path"] == "2.标题":
                    # 特殊标记：标题节点的规则
                    rule_dict["标题节点"] = True
                    # 如果是标题节点，添加范围字段（如果不存在）
                    if "④范围" in rule_dict:
                        rule_dict["范围"] = rule_dict["④范围"]
                    else:
                        rule_dict["范围"] = "整篇文章第一行"
                    # 如果标题规则在要求内容中，把它提取到顶层
                    if "③规则" in rule_dict and "规则" not in rule_dict:
                        rule_dict["规则"] = rule_dict["③规则"]
                
                # 记录判断条件结果
                logger.info(f"  条件1（直接包含规则字段）: {condition1}")
                logger.info(f"  条件2（包含要求内容子节点）: {condition2}")
                logger.info(f"  条件3（主要段落节点）: {condition3}")
                
                # 如果满足任一条件，则为可评分规则
                if condition1 or condition2 or condition3:
                    logger.info(f"  规则 {rule_index} ({rule['path']}) 是可评分规则")
                    
                    # 为了简化处理，确保所有规则都有范围字段
                    if "范围" not in rule_dict:
                        # 如果有④范围字段，使用它
                        if "④范围" in rule_dict:
                            rule_dict["范围"] = rule_dict["④范围"]
                        else:
                            # 否则设置为默认值
                            rule_dict["范围"] = "全文"
                    
                    # 确保规则有相应的规则字段
                    if "规则" not in rule_dict and "③规则" in rule_dict:
                        rule_dict["规则"] = rule_dict["③规则"]
                    
                    scorable_rules.append(rule)
                else:
                    logger.info(f"  规则 {rule_index} 不是可评分规则")
            else:
                logger.info(f"  规则 {rule_index} 不包含rule字段或rule不是字典类型")
        
        # 添加调试日志，输出所有找到的可评分规则
        logger.info(f"找到 {len(scorable_rules)} 条可评分规则:")
        for idx, rule in enumerate(scorable_rules, 1):
            logger.info(f"规则 {idx}: 路径={rule['path']}, 范围={rule['rule'].get('范围', '全文')}")
        
        return scorable_rules
    
    def parse_score_from_rule(self, rule_path: str) -> int:
        """
        从规则路径或规则字典中解析分数
        
        Args:
            rule_path (str): 规则路径，如"首段-共5分.解释题干-共1分"、"第一段.解释题干"或"标题"
            
        Returns:
            int: 分数值
        """
        # 首先尝试在扁平化规则中查找对应的规则项
        rule_item = next((item for item in self.flattened_rules if item["path"] == rule_path), None)
        
        # 如果找到规则项并且包含"分额"字段，直接使用该值
        if rule_item and "rule" in rule_item and isinstance(rule_item["rule"], dict) and "分额" in rule_item["rule"]:
            return rule_item["rule"]["分额"]
            
        # 如果规则路径包含"要求内容"，检查父节点是否有分额
        if ".要求内容" in rule_path:
            parent_path = rule_path.split(".要求内容")[0]
            parent_item = next((item for item in self.flattened_rules if item["path"] == parent_path), None)
            if parent_item and "rule" in parent_item and isinstance(parent_item["rule"], dict) and "分额" in parent_item["rule"]:
                return parent_item["rule"]["分额"]
        
        # 如果没有找到规则项或者没有"分额"字段，则从规则路径提取分数
        # 查找类似"共X分"的模式
        match = re.search(r'共(\d+)分', rule_path)
        if match:
            return int(match.group(1))
        
        # 如果没有找到，尝试查找直接的数字后跟"分"
        match = re.search(r'(\d+)分', rule_path)
        if match:
            return int(match.group(1))
        
        # 默认返回0分
        return 0
