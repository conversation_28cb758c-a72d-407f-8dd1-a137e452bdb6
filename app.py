#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import os
import sys
import traceback
from typing import Dict, List, Tuple, Any, Optional, Union

try:
    from colorama import init, Fore, Style  # type: ignore
except ImportError:
    # 如果没有 colorama，定义空的替代品
    def init(): pass
    class Fore:
        RED = ""
        GREEN = ""
        YELLOW = ""
        BLUE = ""
        MAGENTA = ""
        CYAN = ""
        WHITE = ""
    class Style:
        RESET_ALL = ""

try:
    import nltk  # type: ignore
except ImportError:
    nltk = None

try:
    import openai  # type: ignore
except ImportError:
    openai = None

# 自定义模块导入
import config
from utils.logging_utils import get_logger, setup_logger
from utils.exceptions import EssayGraderError, FileError

from RuleProcessor import RuleProcessor
from ScoreManager import ScoreManager
from TextProcessor import TextProcessor
from LLMInterface import LLMInterface
from EssayGrader import EssayGrader

# 初始化colorama，用于彩色输出
init()

# 初始化统一配置
config.initialize_logging()
logger = get_logger(__name__)

# 尝试下载nltk数据（如果尚未下载）
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='作文自动批改系统')
    parser.add_argument('--rules', '-r', required=True, help='批改规则JSON文件路径')
    parser.add_argument('--essay', '-e', required=True, help='学员作文文件路径')
    parser.add_argument('--title', '-t', help='题目文件路径')
    parser.add_argument('--output', '-o', default=config.DEFAULT_OUTPUT_FILE, help='输出结果文件路径')
    parser.add_argument('--model', '-m', default=config.DEFAULT_MODEL, help='使用的大模型')
    parser.add_argument('--api-key', '-k', help='API密钥')
    parser.add_argument('--base-url', '-b', help='API基础URL')
    parser.add_argument('--verbose', '-v', action='store_true', help='启用详细输出模式')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    parser.add_argument('--debug-log', '-d', default=config.API_DEBUG_LOG, help='API调试日志文件路径')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    try:
        if not os.path.exists(args.rules):
            raise FileError(f"规则文件 {args.rules} 不存在")
        
        if not os.path.exists(args.essay):
            raise FileError(f"作文文件 {args.essay} 不存在")
        
        # 处理题目
        title = ""
        if args.title:
            if os.path.exists(args.title):
                with open(args.title, 'r', encoding='utf-8') as f:
                    title = f.read().strip()
            else:
                raise FileError(f"题目文件 {args.title} 不存在")
        else:
            # 如果未提供题目文件，从作文文件的第一行获取题目
            with open(args.essay, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    title = lines[0].strip()
                    logger.info(f"已从作文文件第一行获取题目: {title}")
                else:
                    logger.warning("作文文件为空，无法获取题目")
                    title = "未知题目"
        
        # 创建评分器并评分
        grader = EssayGrader(
            title=title,
            rules_file=args.rules,
            essay_file=args.essay,
            model_name=args.model,
            api_key=args.api_key,
            base_url=args.base_url,
            verbose=args.verbose,
            debug_log_file=args.debug_log
        )
        
        # 保存评分结果
        grader.save_result(args.output)
        
        print(Fore.GREEN + f"评分完成！结果已保存到 {args.output}" + Style.RESET_ALL)
        print(Fore.CYAN + f"API调试日志已保存到 {args.debug_log}" + Style.RESET_ALL)
        
        # 如果在交互模式下，显示评分摘要
        if args.interactive:
            print("\n" + grader.score_manager.get_score_summary())
    
    except EssayGraderError as e:
        logger.error(f"运行错误: {str(e)}")
        print(Fore.RED + f"错误: {str(e)}" + Style.RESET_ALL)
        sys.exit(1)
    except Exception as e:
        logger.error(f"未预期的错误: {str(e)}")
        logger.error(traceback.format_exc())
        print(Fore.RED + f"未预期的错误: {str(e)}" + Style.RESET_ALL)
        sys.exit(1)


if __name__ == "__main__":
    main() 

